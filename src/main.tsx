import './i18n';
import './styles.css';
import '@mayo/mayo-ui-beta/dist/index2.css';
import '@mayo/mayo-ui-beta/dist/apollo.css';

import { StrictMode } from 'react';
import { CookiesProvider } from 'react-cookie';
import { createRoot } from 'react-dom/client';

import App from './App.tsx';
import QueryProvider from './providers/QueryProvider.tsx';
import { isDev } from './utils/config.ts';

// 檢查是否需要啟動 MSW
const enableMSW = isDev; // 在開發環境啟用

async function enableMocking() {
  if (!enableMSW) return;

  if (typeof window !== 'undefined') {
    const { startMSW } = await import('./mocks/browser');
    return startMSW();
  }
}

enableMocking().then(() => {
  createRoot(document.getElementById('root')!).render(
    <StrictMode>
      <QueryProvider>
        <CookiesProvider>
          <App />
        </CookiesProvider>
      </QueryProvider>
    </StrictMode>,
  );
});
