/**
 *  axios 實例設定與攔截器
 *
 *  - 創建 axios 實例
 *  - 請求攔截器（附帶 token）
 *  - 響應攔截器
 */

import axios, {
  AxiosError,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosRequestHeaders,
  InternalAxiosRequestConfig,
} from 'axios';

import { CookieNameEnum } from '@/types/enums/common/cookie';
import { toCamelCase } from '@/utils/common';
import { getCookie } from '@/utils/cookie';

// API 基礎 URL
const API_BASE_URL = '/api'; // TODO 改成環境變數： import.meta.env.VITE_SERVER_ENV_FD_BACKEND_SERVER,

// 創建 axios 實例
export const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  // headers: {
  //   'Content-Type': 'application/json',
  //   // Authorization: getCookie(CookieNameEnum.AUTH_TOKEN) || '',
  // },
  withCredentials: true,
});

// 請求攔截器（附帶 token）
apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 請求日誌
    console.log(`🚀 API 請求: ${config.method?.toUpperCase()} ${config.url}`);

    const newConfig: AxiosRequestConfig = {
      ...config,
      headers: {
        ...(config.headers as AxiosRequestHeaders),
        // 添加認證 token
        Authorization: getCookie(CookieNameEnum.AUTH_TOKEN) || '',
      },
      withCredentials: true,
    };

    return newConfig as InternalAxiosRequestConfig;
  },
  (error: AxiosError) => {
    console.error('請求攔截器錯誤:', error);
    return Promise.reject(error);
  },
);

// 響應攔截器
apiClient.interceptors.response.use(
  response => {
    // 成功響應處理
    console.log(`✅ API 響應: ${response.status} ${response.config.url}`);

    const contentType = response.headers['content-type'] || '';

    // 如果 content-type 包含 application/json，則轉換為 camelCase
    if (contentType.includes('application/json')) {
      response.data = toCamelCase(response.data);
    }

    return response.data; // 直接返回 data，簡化調用
  },
  (error: AxiosError) => {
    // 錯誤響應處理
    console.error('❌ API 請求失敗:', {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      message: error.message,
    });

    // 統一錯誤格式
    let errorMessage = 'API 請求失敗';

    if (error.response?.data && typeof error.response.data === 'object') {
      const responseData = error.response.data as { message?: string };
      errorMessage = responseData.message || errorMessage;
    } else if (error.message) {
      errorMessage = error.message;
    }

    // 根據狀態碼提供更具體的錯誤信息
    if (error.response?.status) {
      switch (error.response.status) {
        case 401:
          errorMessage = '未授權訪問，請重新登入';
          break;
        case 403:
          errorMessage = '權限不足';
          break;
        case 404:
          errorMessage = '請求的資源不存在';
          break;
        case 500:
          errorMessage = '服務器內部錯誤';
          break;
        default:
          break;
      }
    }

    return Promise.reject(new Error(errorMessage));
  },
);

export default apiClient;
