import { loadDayjsPlugin, Toaster } from '@mayo/mayo-ui-beta/v2';
import { useSetAtom } from 'jotai';
import { FC, PropsWithChildren, Suspense, useEffect } from 'react';

import { useGetUserInfo } from '@/hooks/api/user/useGetUserInfo';
import { useInitializeEmbedMode } from '@/hooks/useEmbedMode';
import { userInfoAtom } from '@/lib/jotai/user';
import { CookieNameEnum } from '@/types/enums/common/cookie';
import { getCookie } from '@/utils/cookie';

import Sidebar from './sidebar';

// 載入 dayjs plugin
loadDayjsPlugin();

const Layout: FC<PropsWithChildren> = ({ children }) => {
  const isLoggedIn = Boolean(getCookie(CookieNameEnum.AUTH_TOKEN));

  const setUserInfo = useSetAtom(userInfoAtom);
  const fetchUserInfo = useGetUserInfo({ enabled: isLoggedIn });
  const initializeEmbedMode = useInitializeEmbedMode();

  // 初始化 embed mode（只在組件掛載時執行一次）
  useEffect(() => {
    initializeEmbedMode();
  }, [initializeEmbedMode]);

  useEffect(() => {
    if (fetchUserInfo.data) setUserInfo(fetchUserInfo.data);
  }, [fetchUserInfo.data, setUserInfo]);

  return (
    <div className="">
      <Suspense fallback={<div>Loading...</div>}>
        <Sidebar>{children}</Sidebar>
        <Toaster />
      </Suspense>
    </div>
  );
};

export default Layout;
