import 'dayjs/locale/zh-cn';

import dayjs from 'dayjs';
import React, { memo, useEffect, useRef, useState } from 'react';
import type { DropTargetMonitor } from 'react-dnd';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

// 自定義CSS樣式，用於隱藏滾動條和圈選效果
const scrollbarStyles = `
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .hide-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  
  .shift-cell.selected {
    border: 2px solid #3b82f6 !important;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  }
  
  .shift-cell.drag-over {
    background-color: rgba(16, 185, 129, 0.2) !important;
  }
  
  .selection-box {
    position: absolute;
    border: 1px dashed #3b82f6;
    background-color: rgba(59, 130, 246, 0.1);
    z-index: 100;
  }
  
  .shift-item {
    user-select: none;
  }
  
  .shift-item.selected {
    outline: 2px solid #3b82f6;
  }
  
  .preview-shift {
    border: 1px dashed #3b82f6;
    background-color: rgba(59, 130, 246, 0.2);
    padding: 1px;
    margin-top: 4px;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    text-align: center;
    color: #3b82f6;
  }
`;

// 操作模式定義
type OperationMode = 'SELECT' | 'DRAG';

// 排班類型定義
interface ShiftType {
  id: string;
  title: string;
  color: string;
}

// 員工定義
interface Employee {
  id: string;
  name: string;
}

// 排班項目定義
interface ShiftItem {
  id: string;
  employeeId: string;
  date: string; // 格式：YYYY-MM-DD
  shiftTypeId: string;
}

// 選中班次接口
interface SelectedShiftItem extends ShiftItem {
  cellId: string; // 用於標識單元格，格式為：employeeId-date
}

// 拖拽項目接口
interface DragItem {
  type: string;
  shiftTypeId: string;
}

// 拖拽選中班次接口
interface DragSelectedShifts {
  type: string;
  shifts: SelectedShiftItem[];
  didDrop: boolean;
}

// 拖拽選中的單個班次接口
interface DragSelectedShift {
  type: string;
  shiftId: string;
}

// 排班類型數據
const shiftTypes: ShiftType[] = [
  { id: 'early', title: '早班', color: '#4F46E5' },
  { id: 'middle', title: '中班', color: '#0EA5E9' },
  { id: 'late', title: '晚班', color: '#10B981' },
  { id: 'rest', title: '休', color: '#F59E0B' },
  { id: 'holiday', title: '休假日', color: '#6366F1' },
  { id: 'example', title: '例假日', color: '#EC4899' },
  { id: 'fixed', title: '固定日', color: '#8B5CF6' },
];

// 員工數據
const employees: Employee[] = [
  { id: '1', name: '王小明' },
  { id: '2', name: '李大仁' },
  { id: '3', name: '陳佐承' },
  { id: '4', name: '張美美' },
  { id: '5', name: '林志豪' },
  { id: '6', name: '黃雅芳' },
  { id: '7', name: '吳俊傑' },
  { id: '8', name: '楊詩涵' },
  { id: '9', name: '趙宇恆' },
  { id: '10', name: '許雅婷' },
  { id: '11', name: '周家豪' },
  { id: '12', name: '郭芳瑜' },
  { id: '13', name: '陳建志' },
  { id: '14', name: '蔡宜臻' },
  { id: '15', name: '蕭志偉' },
  { id: '16', name: '沈佳穎' },
  { id: '17', name: '劉俊宏' },
  { id: '18', name: '鄭雅文' },
  { id: '19', name: '何志明' },
  { id: '20', name: '謝雅雯' },
];

// 拖拽排班類型組件
const DraggableShiftType: React.FC<{ shiftType: ShiftType }> = ({ shiftType }) => {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'SHIFT_TYPE',
    item: {
      type: 'SHIFT_TYPE',
      shiftTypeId: shiftType.id,
    },
    collect: monitor => ({
      isDragging: !!monitor.isDragging(),
    }),
  }));

  return (
    <div
      ref={drag}
      className="cursor-move rounded px-2 py-1 text-center text-white"
      style={{
        backgroundColor: shiftType.color,
        opacity: isDragging ? 0.5 : 1,
        border: '1px solid #ccc',
        width: '60px',
        display: 'inline-block',
        margin: '0 4px',
      }}
    >
      {shiftType.title}
    </div>
  );
};

// 排班系統主組件
const ShiftScheduler: React.FC = () => {
  const [currentMonth, setCurrentMonth] = useState(dayjs());
  const [shifts, setShifts] = useState<ShiftItem[]>([]);
  const [selectedShifts, setSelectedShifts] = useState<SelectedShiftItem[]>([]);
  const [operationMode, setOperationMode] = useState<OperationMode>('SELECT');
  const [selectionBox, setSelectionBox] = useState<{
    startX: number;
    startY: number;
    endX: number;
    endY: number;
    isSelecting: boolean;
  }>({
    startX: 0,
    startY: 0,
    endX: 0,
    endY: 0,
    isSelecting: false,
  });

  const tableRef = useRef<HTMLTableElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 添加額外的狀態來跟踪活躍的預覽單元格
  const [activePreviewCells, setActivePreviewCells] = useState<string[]>([]);

  // 生成當月的所有日期
  const getDaysInMonth = () => {
    const daysInMonth = currentMonth.daysInMonth();
    const days = [];
    for (let i = 1; i <= daysInMonth; i++) {
      days.push(currentMonth.date(i).format('YYYY-MM-DD'));
    }
    return days;
  };

  // 處理排班拖拽
  const handleDropShift = (employeeId: string, date: string, shiftTypeId: string) => {
    // 創建新的排班項
    const newShift: ShiftItem = {
      id: `${employeeId}-${date}-${Date.now()}`, // 生成唯一ID
      employeeId,
      date,
      shiftTypeId,
    };

    // 更新排班數據
    setShifts(prevShifts => {
      // 過濾掉同一天同一員工的舊排班
      const filteredShifts = prevShifts.filter(
        shift => !(shift.employeeId === employeeId && shift.date === date),
      );
      return [...filteredShifts, newShift];
    });
  };

  // 處理選擇班次
  const handleSelectShift = (shift: ShiftItem, cellId: string, multiSelect: boolean) => {
    const selectedShift: SelectedShiftItem = { ...shift, cellId };

    setSelectedShifts(prev => {
      // 檢查是否已經選中
      const isAlreadySelected = prev.some(s => s.id === shift.id);

      if (isAlreadySelected) {
        // 如果已經選中，則取消選中
        return prev.filter(s => s.id !== shift.id);
      } else if (multiSelect) {
        // 如果是多選，添加到已選中列表
        return [...prev, selectedShift];
      } else {
        // 如果是單選，替換已選中列表
        return [selectedShift];
      }
    });
  };

  // 處理拖拽選中的班次
  const handleDropSelectedShifts = (
    targetEmployeeId: string,
    targetDate: string,
    shifts: SelectedShiftItem[],
  ) => {
    // 根據目標日期計算偏移量
    const targetDateObj = dayjs(targetDate);

    // 獲取所有選中班次的日期
    const dates = Array.from(new Set(shifts.map(s => s.date))).sort();

    if (dates.length === 0) return;

    // 計算日期偏移量
    const firstDateObj = dayjs(dates[0]);
    const daysDiff = targetDateObj.diff(firstDateObj, 'day');

    // 創建新的班次
    const newShifts: ShiftItem[] = [];

    shifts.forEach(shift => {
      const shiftDate = dayjs(shift.date);
      const newDate = shiftDate.add(daysDiff, 'day').format('YYYY-MM-DD');

      const newShift: ShiftItem = {
        id: `${targetEmployeeId}-${newDate}-${Date.now()}-${Math.random()}`,
        employeeId: targetEmployeeId,
        date: newDate,
        shiftTypeId: shift.shiftTypeId,
      };

      newShifts.push(newShift);
    });

    // 更新排班數據
    setShifts(prevShifts => {
      // 過濾掉會被新班次覆蓋的舊班次
      const filteredShifts = prevShifts.filter(shift => {
        return !newShifts.some(
          newShift => newShift.employeeId === shift.employeeId && newShift.date === shift.date,
        );
      });

      return [...filteredShifts, ...newShifts];
    });

    // 拖曳完成後，清除選擇
    handleClearSelection();
  };

  // 處理月份切換
  const handlePrevMonth = () => {
    setCurrentMonth(currentMonth.subtract(1, 'month'));
  };

  const handleNextMonth = () => {
    setCurrentMonth(currentMonth.add(1, 'month'));
  };

  // 處理鼠標事件，實現框選功能
  const handleMouseDown = (e: React.MouseEvent) => {
    if (operationMode !== 'SELECT' || e.button !== 0) return;

    // 只在表格區域內的點擊觸發框選
    if (!containerRef.current || !tableRef.current) return;
    const tableRect = tableRef.current.getBoundingClientRect();

    if (
      e.clientX < tableRect.left ||
      e.clientX > tableRect.right ||
      e.clientY < tableRect.top ||
      e.clientY > tableRect.bottom
    ) {
      return;
    }

    // 開始框選
    setSelectionBox({
      startX: e.clientX,
      startY: e.clientY,
      endX: e.clientX,
      endY: e.clientY,
      isSelecting: true,
    });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!selectionBox.isSelecting) return;

    setSelectionBox(prev => ({
      ...prev,
      endX: e.clientX,
      endY: e.clientY,
    }));
  };

  const handleMouseUp = () => {
    if (!selectionBox.isSelecting) return;

    // 計算選區
    const left = Math.min(selectionBox.startX, selectionBox.endX);
    const top = Math.min(selectionBox.startY, selectionBox.endY);
    const right = Math.max(selectionBox.startX, selectionBox.endX);
    const bottom = Math.max(selectionBox.startY, selectionBox.endY);

    // 收集在選區內的班次
    if (tableRef.current) {
      const shiftElements = tableRef.current.querySelectorAll('.shift-cell');
      const selectedCells: string[] = [];

      shiftElements.forEach(element => {
        const rect = element.getBoundingClientRect();

        // 檢查是否有交集
        if (rect.left < right && rect.right > left && rect.top < bottom && rect.bottom > top) {
          const cellId = element.getAttribute('data-cell-id');
          if (cellId) {
            selectedCells.push(cellId);
          }
        }
      });

      // 找出選中單元格中的班次
      const newSelectedShifts = shifts
        .filter(shift => {
          const cellId = `${shift.employeeId}-${shift.date}`;
          return selectedCells.includes(cellId);
        })
        .map(shift => ({
          ...shift,
          cellId: `${shift.employeeId}-${shift.date}`,
        }));

      // 更新選中的班次
      if (newSelectedShifts.length > 0) {
        setSelectedShifts(newSelectedShifts);
      }
    }

    // 重置選區
    setSelectionBox({
      startX: 0,
      startY: 0,
      endX: 0,
      endY: 0,
      isSelecting: false,
    });
  };

  // 清除選中的班次
  const handleClearSelection = () => {
    setSelectedShifts([]);
  };

  // 設置預覽班次的核心函數
  const setHoveredCell = (cellId: string | null, shifts: SelectedShiftItem[] = []) => {
    if (!cellId) {
      setActivePreviewCells([]);
      return;
    }

    // 計算需要預覽的所有單元格
    if (shifts.length > 0 && cellId) {
      const [targetEmployeeId, targetDate] = cellId.split('-');
      const targetDateObj = dayjs(targetDate);

      // 獲取選中班次的日期，並按日期排序
      const selectedDates = Array.from(new Set(shifts.map(s => s.date))).sort();
      if (selectedDates.length === 0) return;

      // 計算日期偏移量
      const firstDateObj = dayjs(selectedDates[0]);
      const daysDiff = targetDateObj.diff(firstDateObj, 'day');

      // 計算所有需要預覽的單元格ID
      const previewCellIds = selectedDates.map(date => {
        const shiftDate = dayjs(date);
        const newDate = shiftDate.add(daysDiff, 'day').format('YYYY-MM-DD');
        return `${targetEmployeeId}-${newDate}`;
      });

      setActivePreviewCells(previewCellIds);
    }
  };

  // 清除所有預覽
  const clearAllPreviews = () => {
    setActivePreviewCells([]);
  };

  // 處理拖拽結束
  const handleDragEnd = () => {
    clearAllPreviews();
  };

  useEffect(() => {
    // 設置為中文
    dayjs.locale('zh-cn');

    // 添加拖拽結束事件監聽
    document.addEventListener('dragend', handleDragEnd);

    return () => {
      document.removeEventListener('dragend', handleDragEnd);
    };
  }, []);

  const days = getDaysInMonth();

  const renderSelectionBox = () => {
    if (!selectionBox.isSelecting) return null;

    const left = Math.min(selectionBox.startX, selectionBox.endX);
    const top = Math.min(selectionBox.startY, selectionBox.endY);
    const width = Math.abs(selectionBox.endX - selectionBox.startX);
    const height = Math.abs(selectionBox.endY - selectionBox.startY);

    return (
      <div
        className="selection-box"
        style={{
          left,
          top,
          width,
          height,
        }}
      />
    );
  };

  // 班次顯示組件
  const ShiftDisplay: React.FC<{
    shift: ShiftItem;
    isSelected: boolean;
    cellId: string;
  }> = ({ shift, isSelected, cellId }) => {
    const shiftType = shiftTypes.find(type => type.id === shift.shiftTypeId);

    // 使用drag hook處理拖拽
    const [{ isDragging }, drag] = useDrag(() => ({
      type: 'SELECTED_SHIFT',
      item: {
        type: 'SELECTED_SHIFT',
        shiftId: shift.id,
      },
      canDrag: () => operationMode === 'DRAG' && isSelected,
      collect: monitor => ({
        isDragging: !!monitor.isDragging(),
      }),
    }));

    // 處理點擊事件
    const handleClick = (e: React.MouseEvent) => {
      if (operationMode === 'SELECT') {
        const multiSelect = e.ctrlKey || e.metaKey || e.shiftKey;
        handleSelectShift(shift, cellId, multiSelect);
      }
    };

    return (
      <div
        ref={drag}
        className={`shift-item mt-1 cursor-pointer rounded p-1 text-center text-xs text-white ${isSelected ? 'selected' : ''}`}
        style={{
          backgroundColor: shiftType?.color,
          cursor: operationMode === 'DRAG' && isSelected ? 'move' : 'pointer',
          opacity: isDragging ? 0.5 : 1,
        }}
        onClick={handleClick}
        data-shift-id={shift.id}
      >
        {shiftType?.title}
      </div>
    );
  };

  // 日曆單元格組件
  const CalendarCell: React.FC<{
    date: string;
    employeeId: string;
  }> = memo(({ date, employeeId }) => {
    const [previewShifts, setPreviewShifts] = useState<ShiftItem[]>([]);

    // 處理拖拽排班類型
    const [{ isOverShiftType }, dropShiftType] = useDrop(() => ({
      accept: 'SHIFT_TYPE',
      drop: (item: DragItem) => {
        const dragItem = item as DragItem;
        handleDropShift(employeeId, date, dragItem.shiftTypeId);
      },
      collect: (monitor: DropTargetMonitor) => ({
        isOverShiftType: !!monitor.isOver(),
      }),
    }));

    // 處理拖拽選中的班次
    const [{ isOverSelectedShifts }, dropSelectedShifts] = useDrop(() => ({
      accept: ['SELECTED_SHIFTS', 'SELECTED_SHIFT'],
      hover: (item: DragSelectedShifts | DragSelectedShift, monitor) => {
        if (!monitor.isOver({ shallow: true })) return;

        const cellId = `${employeeId}-${date}`;

        if (item.type === 'SELECTED_SHIFTS') {
          const dragItem = item as DragSelectedShifts;
          generatePreviewShifts(dragItem.shifts);
          setHoveredCell(cellId, dragItem.shifts);
        } else if (item.type === 'SELECTED_SHIFT') {
          const dragItem = item as DragSelectedShift;
          // 找到選中的班次
          const shift = selectedShifts.find(s => s.id === dragItem.shiftId);
          if (shift) {
            generatePreviewShifts(selectedShifts);
            setHoveredCell(cellId, selectedShifts);
          }
        }
      },
      drop: (item: DragSelectedShifts | DragSelectedShift) => {
        if (item.type === 'SELECTED_SHIFTS') {
          const dragItem = item as DragSelectedShifts;
          handleDropSelectedShifts(employeeId, date, dragItem.shifts);
          clearAllPreviews();
          return { didDrop: true };
        } else if (item.type === 'SELECTED_SHIFT') {
          const dragItem = item as DragSelectedShift;
          // 找到選中的班次
          const selectedShift = selectedShifts.find(s => s.id === dragItem.shiftId);
          if (selectedShift) {
            // 拖動一個班次時，將所有選中的班次都拖動
            handleDropSelectedShifts(employeeId, date, selectedShifts);
            clearAllPreviews();
            return { didDrop: true };
          }
        }

        // 清除預覽
        setPreviewShifts([]);
        return undefined;
      },
      collect: (monitor: DropTargetMonitor) => ({
        isOverSelectedShifts: !!monitor.isOver(),
      }),
    }));

    // 生成預覽班次
    const generatePreviewShifts = (shifts: SelectedShiftItem[]) => {
      // 創建預覽班次
      const newShifts: ShiftItem[] = [];

      // 只顯示一個預覽班次
      if (shifts.length > 0) {
        const previewShift: ShiftItem = {
          id: `preview-${Date.now()}`,
          employeeId,
          date,
          shiftTypeId: shifts[0].shiftTypeId,
        };

        newShifts.push(previewShift);
      }

      setPreviewShifts(newShifts);
    };

    // 合併ref
    const ref = (element: HTMLTableCellElement) => {
      dropShiftType(element);
      dropSelectedShifts(element);
    };

    // 查找該日期該員工的排班
    const employeeShifts = shifts.filter(
      shift => shift.employeeId === employeeId && shift.date === date,
    );

    // 判斷單元格是否有選中的班次
    const cellId = `${employeeId}-${date}`;
    const isCellSelected = employeeShifts.some(shift =>
      selectedShifts.some(s => s.id === shift.id),
    );

    // 判斷是否是活躍的預覽單元格
    const isActivePreviewCell = activePreviewCells.includes(cellId);

    // 渲染預覽班次
    const renderPreviewShifts = () => {
      if (!(isOverSelectedShifts || isActivePreviewCell) || previewShifts.length === 0) return null;

      const shiftTypeCount: { [key: string]: number } = {};

      // 計算每種班次類型的數量
      selectedShifts.forEach(shift => {
        if (shiftTypeCount[shift.shiftTypeId]) {
          shiftTypeCount[shift.shiftTypeId]++;
        } else {
          shiftTypeCount[shift.shiftTypeId] = 1;
        }
      });

      // 顯示已選中的班次數量提示
      return <div className="preview-shift">已選擇 {selectedShifts.length} 個班次</div>;
    };

    return (
      <td
        ref={ref}
        className={`shift-cell border border-gray-200 p-1 ${isOverShiftType ? 'bg-blue-50' : ''} ${isOverSelectedShifts || isActivePreviewCell ? 'drag-over' : ''} ${isCellSelected ? 'selected' : ''}`}
        style={{
          minWidth: '80px',
          height: '60px',
          verticalAlign: 'top',
        }}
        data-cell-id={cellId}
        onMouseLeave={() => {
          setPreviewShifts([]);
        }}
      >
        {employeeShifts.map(shift => (
          <ShiftDisplay
            key={shift.id}
            shift={shift}
            isSelected={selectedShifts.some(s => s.id === shift.id)}
            cellId={cellId}
          />
        ))}
        {renderPreviewShifts()}
      </td>
    );
  });

  // 可拖拽的選中班次組件
  const DraggableSelectedShifts: React.FC<{
    operationMode: string;
    selectedCells: string[];
    shifts: SelectedShiftItem[];
  }> = ({ operationMode, selectedCells, shifts }) => {
    const [{ isDragging }, drag] = useDrag(
      () => ({
        type: 'SELECTED_SHIFTS',
        item: (): DragSelectedShifts => {
          return {
            type: 'SELECTED_SHIFTS',
            shifts: shifts,
            didDrop: false,
          };
        },
        canDrag: () => operationMode === 'DRAG' && selectedCells.length > 0,
        collect: monitor => ({
          isDragging: !!monitor.isDragging(),
        }),
        end: () => {
          clearAllPreviews();
        },
        hover: (_: unknown, monitor: DropTargetMonitor) => {
          const clientOffset = monitor.getClientOffset();
          if (!clientOffset) {
            clearAllPreviews();
            return;
          }

          const hoveredElement = document.elementFromPoint(clientOffset.x, clientOffset.y);
          if (!hoveredElement) {
            clearAllPreviews();
            return;
          }

          // 找到目標單元格
          const cell = hoveredElement.closest('.shift-cell');
          if (!cell) {
            clearAllPreviews();
            return;
          }

          // 獲取單元格的員工ID和日期
          const cellId = cell.getAttribute('data-cell-id');
          if (cellId) {
            setHoveredCell(cellId, shifts);
            return;
          }

          clearAllPreviews();
        },
      }),
      [operationMode, selectedCells, shifts],
    );

    if (selectedCells.length === 0) return null;

    return (
      <div
        ref={drag}
        className="fixed right-4 bottom-4 z-50 rounded-lg bg-white p-4 shadow-lg"
        style={{ opacity: isDragging ? 0.5 : 1 }}
      >
        <div className="mb-2 flex items-center justify-between">
          <span className="font-medium">已選擇 {selectedCells.length} 個班次</span>
          <button onClick={handleClearSelection} className="text-red-500 hover:text-red-700">
            清除選擇
          </button>
        </div>
        <div className="text-sm text-gray-500">拖曳此區域到目標日期進行複製</div>
      </div>
    );
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <style>{scrollbarStyles}</style>
      <div
        className="flex h-full w-full flex-col"
        ref={containerRef}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        {/* 操作模式切換 */}
        <div className="flex items-center space-x-2 border-b border-gray-300 bg-white px-3 py-2">
          <div className="font-medium">操作模式:</div>
          <button
            className={`rounded px-3 py-1 ${operationMode === 'SELECT' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
            onClick={() => setOperationMode('SELECT')}
          >
            選取模式
          </button>
          <button
            className={`rounded px-3 py-1 ${operationMode === 'DRAG' ? 'bg-green-500 text-white' : 'bg-gray-200'}`}
            onClick={() => setOperationMode('DRAG')}
            disabled={selectedShifts.length === 0}
          >
            拖曳模式
          </button>

          {selectedShifts.length > 0 && (
            <span className="ml-4 text-blue-600">已選擇 {selectedShifts.length} 個班次</span>
          )}

          {selectedShifts.length > 0 && (
            <button
              className="ml-auto rounded bg-red-100 px-3 py-1 text-red-600"
              onClick={handleClearSelection}
            >
              清除選擇
            </button>
          )}
        </div>

        {/* 頂部排班類型按鈕區 */}
        <div className="hide-scrollbar flex items-center space-x-2 overflow-auto border-b border-gray-300 bg-gray-100 p-3">
          <div className="mr-2 font-medium">排班類型:</div>
          {shiftTypes.map(shiftType => (
            <DraggableShiftType key={shiftType.id} shiftType={shiftType} />
          ))}
        </div>

        {/* 月份導航 */}
        <div className="flex items-center justify-between border-b border-gray-300 bg-white p-3">
          <button className="rounded bg-blue-500 px-4 py-1 text-white" onClick={handlePrevMonth}>
            上個月
          </button>
          <h2 className="text-xl font-bold">{currentMonth.format('YYYY年MM月')}</h2>
          <button className="rounded bg-blue-500 px-4 py-1 text-white" onClick={handleNextMonth}>
            下個月
          </button>
        </div>

        {/* 日曆主體 - 使用 Flexbox */}
        <div className="hide-scrollbar flex-1 overflow-auto" ref={tableRef}>
          <div className="flex min-w-fit flex-col" role="grid">
            {/* 表頭 */}
            <div className="sticky top-0 z-20 flex bg-gray-100" role="row" aria-rowindex={1}>
              <div
                className="sticky left-0 z-30 min-w-[100px] border border-gray-300 bg-gray-100 p-2 font-medium"
                role="columnheader"
              >
                員工
              </div>
              {days.map((day, index) => {
                const dayObj = dayjs(day);
                const isWeekend = dayObj.day() === 0 || dayObj.day() === 6;
                const weekdayMap = {
                  0: '日',
                  1: '一',
                  2: '二',
                  3: '三',
                  4: '四',
                  5: '五',
                  6: '六',
                };

                return (
                  <div
                    key={day}
                    className={`min-w-[80px] flex-1 border border-gray-300 p-1 text-center ${isWeekend ? 'bg-gray-200' : ''}`}
                    role="columnheader"
                    aria-colindex={index + 2}
                    data-date={day}
                  >
                    <div>{dayObj.date()}</div>
                    <div className="text-xs text-gray-500">
                      週{weekdayMap[dayObj.day() as keyof typeof weekdayMap]}
                    </div>
                  </div>
                );
              })}
            </div>

            {/* 表格內容 */}
            {employees.map((employee, rowIndex) => (
              <div key={employee.id} className="flex" role="row" aria-rowindex={rowIndex + 2}>
                <div
                  className="sticky left-0 z-10 min-w-[100px] border border-gray-300 bg-gray-50 p-2 font-medium"
                  role="rowheader"
                >
                  {employee.name}
                </div>
                {days.map((day, colIndex) => (
                  <div
                    key={`${employee.id}-${day}`}
                    className="min-w-[80px] flex-1"
                    role="gridcell"
                    aria-colindex={colIndex + 2}
                  >
                    <CalendarCell date={day} employeeId={employee.id} />
                  </div>
                ))}
              </div>
            ))}
          </div>
        </div>

        {/* 渲染選區框 */}
        {renderSelectionBox()}

        {/* 可拖拽的選中班次 */}
        {operationMode === 'DRAG' && (
          <DraggableSelectedShifts
            operationMode={operationMode}
            selectedCells={selectedShifts.map(s => s.cellId)}
            shifts={selectedShifts}
          />
        )}
      </div>
    </DndProvider>
  );
};

export default ShiftScheduler;
