import { Outlet, type RouteObject } from 'react-router-dom';

import Layout from '@/components/layout';

import basicDemoRoute from './basicDemoRoute';
import demoFormRoute from './DemoFormRoute';
import mswDemoRoute from './mswDemoRoute';
import shiftScheduleApprovalRoute from './shiftScheduleApproval';
import shiftSchedulerRoute from './shiftSchedulerRoute';
import timelineDemoRoute from './timelineDemoRoute';

const defaultRoute = { ...timelineDemoRoute, path: '' };

const appRoutes: RouteObject[] = [
  {
    path: '/',
    element: (
      <Layout>
        <Outlet />
      </Layout>
    ),
    children: [
      defaultRoute,
      timelineDemoRoute,
      shiftSchedulerRoute,
      mswDemoRoute,
      basicDemoRoute,
      demoFormRoute,
      shiftScheduleApprovalRoute,
    ],
  },
];

export default appRoutes;
