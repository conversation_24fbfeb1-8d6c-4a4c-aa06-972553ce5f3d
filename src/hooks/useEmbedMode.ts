import { useAtom, useAtomValue, useSetAtom } from 'jotai';

import { embedMode<PERSON>tom, initializeEmbedMode<PERSON>tom, isEmbeddedAtom, toggleEmbedModeAtom } from '@/lib/jotai/embedMode';

/**
 * 使用 embed mode 狀態的 hook
 * @returns 包含狀態和操作方法的物件
 */
export const useEmbedMode = () => {
  const [isEmbedded, setIsEmbedded] = useAtom(embedModeAtom);
  const initializeEmbedMode = useSetAtom(initializeEmbedModeAtom);
  const toggleEmbedMode = useSetAtom(toggleEmbedModeAtom);

  return {
    isEmbedded,
    setIsEmbedded,
    initializeEmbedMode,
    toggleEmbedMode,
  };
};

/**
 * 只讀取 embed mode 狀態的 hook
 * @returns isEmbedded 狀態
 */
export const useIsEmbedded = () => {
  return useAtomValue(isEmbeddedAtom);
};

/**
 * 初始化 embed mode 的 hook
 * @returns 初始化函數
 */
export const useInitializeEmbedMode = () => {
  return useSetAtom(initializeEmbedModeAtom);
};
