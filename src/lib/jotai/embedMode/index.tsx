import { atom } from 'jotai';

// Constants
const IFRAME_MODE_PARAMS = ['isIframeMode', 'isiframemode', 'isIframemode'] as const;

// 基礎 atom - 只在當次應用程式加載期間保持狀態
export const embedModeAtom = atom<boolean>(false);

// 初始化 atom - 用於應用程式啟動時設置狀態
export const initializeEmbedModeAtom = atom(
  null, // 不需要 read
  (_get, set) => {
    // 檢查 query string 中的 embed mode 參數
    const queryString = window.location.search;
    const searchParams = new URLSearchParams(queryString);
    const isEmbeddedFromQuery = IFRAME_MODE_PARAMS.some(
      param => searchParams.get(param) === 'true',
    );

    // 如果 query string 中有 embed mode 參數，則更新狀態
    if (isEmbeddedFromQuery) {
      set(embedModeAtom, true);
    }
    // 如果沒有 query string 參數，則保持預設值 false
  },
);

// 切換 embed mode 的 action atom
export const toggleEmbedModeAtom = atom(
  null, // 不需要 read
  (get, set) => {
    const currentMode = get(embedModeAtom);
    set(embedModeAtom, !currentMode);
  },
);

// 衍生的 read-only atom（可選，提供更語義化的命名）
export const isEmbeddedAtom = atom(get => get(embedModeAtom));
