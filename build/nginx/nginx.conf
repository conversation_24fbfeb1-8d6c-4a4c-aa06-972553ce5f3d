# /etc/nginx/nginx.conf

user nginx;
worker_processes auto;

events {
    worker_connections 1024;  # 最大连接数
}
http {
    types {
        text/html                             html htm shtml;
        text/css                              css;
        text/xml                              xml;
        image/gif                             gif;
        image/jpeg                            jpeg jpg;
        application/javascript                js;
        application/font-woff                 woff;
        application/font-ttf                  ttf;
        application/json                      json;
        application/xml                       xml;
        application/vnd.ms-fontobject         eot;
        image/svg+xml                         svg;
        video/mp4                             mp4;
        video/x-msvideo                       avi;
        video/quicktime                       mov;
        video/mpeg                            mpeg mpg;
        video/webm                            webm;
    }

    server {
            listen       8080;
            server_name  localhost;
            
            gzip  on;  
            gzip_min_length 1k;
            gzip_comp_level 5; 
            gzip_types text/plain application/javascript application/x-javascript text/javascript text/xml text/css;
            gzip_disable "MSIE [1-6]\.";
            gzip_vary on;

            # 健康檢查端點
            location /health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
            }

            location / {
                    root /usr/share/nginx/html;
                    index index.php index.html index.htm;
                    # add_header Cache-Control;
                    add_header Access-Control-Allow-Origin *;
                    if ( $request_uri ~* ^.+.(js|css|jpg|png|gif|tif|dpg|jpeg|eot|svg|ttf|woff|json|mp4|rmvb|rm|wmv|avi|3gp)$ ){
                            add_header Cache-Control max-age=7776000;
                            add_header Access-Control-Allow-Origin *;
                    }
            }
    }
}
